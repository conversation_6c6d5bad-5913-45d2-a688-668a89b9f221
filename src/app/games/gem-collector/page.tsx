"use client";

import { useState, useEffect, useCallback } from 'react';
import { ArrowLeft, Zap } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '../../../components/auth/AuthProvider';
import { useRouter, useSearchParams } from 'next/navigation';
import { SentenceTranslation, SentenceSegment, SentenceSegmentOption } from '../../api/games/gem-collector/sentences/route';
import GemCollectorSettings, { GameSettings } from '../../../components/games/GemCollectorSettings';

interface GemOption {
  id: string;
  text: string;
  isCorrect: boolean;
  lane: number; // 0, 1, or 2 (top, middle, bottom)
  position: number; // x position
  segmentId: string;
  explanation?: string;
}

interface GameSession {
  sessionId: string;
  startTime: Date;
  totalSegments: number;
  correctSegments: number;
  incorrectSegments: number;
  gemsCollected: number;
  speedBoostsUsed: number;
  segmentAttempts: SegmentAttempt[];
}

interface SegmentAttempt {
  segmentId: string;
  selectedOptionId: string;
  isCorrect: boolean;
  responseTime: number;
  gemsEarned: number;
}

interface GameMode {
  type: 'free_play' | 'assignment';
  assignmentId?: string;
  language: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  theme?: string;
  topic?: string;
}

export default function GemCollectorGame() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Game mode and configuration
  const [gameMode, setGameMode] = useState<GameMode>({
    type: 'free_play',
    language: 'spanish',
    difficulty: 'beginner'
  });

  // Game state
  const [sentences, setSentences] = useState<SentenceTranslation[]>([]);
  const [currentSentenceIndex, setCurrentSentenceIndex] = useState(0);
  const [currentSegmentIndex, setCurrentSegmentIndex] = useState(0);
  const [playerLane, setPlayerLane] = useState(1); // 0=top, 1=middle, 2=bottom
  const [gems, setGems] = useState<GemOption[]>([]);
  const [score, setScore] = useState(0);
  const [gameSpeed, setGameSpeed] = useState(2);
  const [speedBoostActive, setSpeedBoostActive] = useState(false);
  const [gameStarted, setGameStarted] = useState(false);
  const [gameOver, setGameOver] = useState(false);
  const [feedback, setFeedback] = useState<{ type: 'correct' | 'wrong' | null; text: string }>({ type: null, text: '' });
  const [lives, setLives] = useState(3);
  const [backgroundPosition, setBackgroundPosition] = useState(0);
  const [builtSentence, setBuiltSentence] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  // Session tracking
  const [gameSession, setGameSession] = useState<GameSession | null>(null);
  const [segmentStartTime, setSegmentStartTime] = useState<Date | null>(null);

  // Initialize game mode from URL parameters
  useEffect(() => {
    const assignmentId = searchParams.get('assignment');
    const language = searchParams.get('language') || 'spanish';
    const difficulty = searchParams.get('difficulty') || 'beginner';
    const theme = searchParams.get('theme') || undefined;
    const topic = searchParams.get('topic') || undefined;

    setGameMode({
      type: assignmentId ? 'assignment' : 'free_play',
      assignmentId: assignmentId || undefined,
      language,
      difficulty: difficulty as 'beginner' | 'intermediate' | 'advanced',
      theme,
      topic
    });
  }, [searchParams]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/auth/login');
    }
  }, [user, isLoading, router]);

  // Fetch sentences when game mode is set
  useEffect(() => {
    if (gameMode && user && !loading) {
      fetchSentences();
    }
  }, [gameMode, user]);

  const fetchSentences = async () => {
    if (loading) return; // Prevent multiple simultaneous requests

    setLoading(true);
    try {
      const response = await fetch('/api/games/gem-collector/sentences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode: gameMode.type,
          assignmentId: gameMode.assignmentId,
          language: gameMode.language,
          difficulty: gameMode.difficulty,
          theme: gameMode.theme,
          topic: gameMode.topic,
          count: 10
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API Error:', response.status, errorData);
        throw new Error(`Failed to fetch sentences: ${response.status}`);
      }

      const data = await response.json();
      setSentences(data.sentences || []);
    } catch (error) {
      console.error('Error fetching sentences:', error);
      // Use fallback sentences if API fails
      setSentences(getFallbackSentences());
    } finally {
      setLoading(false);
    }
  };

  // Fallback sentences for when API fails
  const getFallbackSentences = () => [
    {
      id: 'fallback-1',
      englishSentence: 'I like to go to the cinema',
      targetLanguage: gameMode.language,
      targetSentence: 'Me gusta ir al cine',
      difficultyLevel: gameMode.difficulty,
      theme: 'Leisure and entertainment',
      topic: 'Free time activities',
      grammarFocus: 'gustar-verb',
      curriculumTier: 'Foundation',
      wordCount: 6,
      complexityScore: 30,
      segments: [
        {
          id: 'fallback-seg-1',
          segmentOrder: 1,
          englishSegment: 'I like',
          targetSegment: 'Me gusta',
          segmentType: 'phrase',
          grammarNote: 'Gustar construction',
          options: [
            { id: 'opt-1', optionText: 'Me gusta', isCorrect: true, distractorType: 'correct' },
            { id: 'opt-2', optionText: 'Me encanta', isCorrect: false, distractorType: 'semantic' },
            { id: 'opt-3', optionText: 'Odio', isCorrect: false, distractorType: 'semantic' }
          ]
        },
        {
          id: 'fallback-seg-2',
          segmentOrder: 2,
          englishSegment: 'to go',
          targetSegment: 'ir',
          segmentType: 'word',
          options: [
            { id: 'opt-4', optionText: 'ir', isCorrect: true, distractorType: 'correct' },
            { id: 'opt-5', optionText: 'venir', isCorrect: false, distractorType: 'semantic' },
            { id: 'opt-6', optionText: 'estar', isCorrect: false, distractorType: 'grammatical' }
          ]
        },
        {
          id: 'fallback-seg-3',
          segmentOrder: 3,
          englishSegment: 'to the cinema',
          targetSegment: 'al cine',
          segmentType: 'phrase',
          grammarNote: 'Contraction al = a + el',
          options: [
            { id: 'opt-7', optionText: 'al cine', isCorrect: true, distractorType: 'correct' },
            { id: 'opt-8', optionText: 'del cine', isCorrect: false, distractorType: 'grammatical' },
            { id: 'opt-9', optionText: 'en cine', isCorrect: false, distractorType: 'grammatical' }
          ]
        }
      ]
    }
  ];

  // Get current sentence and segment for display (moved before early returns)
  const currentSentence = sentences[currentSentenceIndex];
  const currentSegment = currentSentence?.segments[currentSegmentIndex];
  const progressPercentage = sentences.length > 0
    ? ((currentSentenceIndex * 100) / sentences.length) + ((currentSegmentIndex * 100) / (sentences.length * (currentSentence?.segments.length || 1)))
    : 0;

  // Show loading while checking authentication or loading sentences
  if (isLoading || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white font-medium">
            {isLoading ? 'Loading game...' : 'Preparing sentences...'}
          </p>
        </div>
      </div>
    );
  }

  // Don't render the game if user is not authenticated
  if (!user) {
    return null;
  }



  // Handle keyboard input
  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    if (!gameStarted || gameOver) return;

    switch (event.key) {
      case 'ArrowUp':
        event.preventDefault();
        setPlayerLane(prev => Math.max(0, prev - 1));
        break;
      case 'ArrowDown':
        event.preventDefault();
        setPlayerLane(prev => Math.min(2, prev + 1));
        break;
      case 'ArrowRight':
        event.preventDefault();
        activateSpeedBoost();
        break;
    }
  }, [gameStarted, gameOver]);

  const activateSpeedBoost = () => {
    if (speedBoostActive) return;

    setSpeedBoostActive(true);
    setGameSpeed(prev => prev * 2);

    // Update session tracking
    if (gameSession) {
      setGameSession(prev => prev ? {
        ...prev,
        speedBoostsUsed: prev.speedBoostsUsed + 1
      } : null);
    }

    // Speed boost lasts for 3 seconds
    setTimeout(() => {
      setSpeedBoostActive(false);
      setGameSpeed(prev => prev / 2);
    }, 3000);
  };

  useEffect(() => {
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);

  // Game loop
  useEffect(() => {
    if (!gameStarted || gameOver) return;

    const gameLoop = setInterval(() => {
      // Move background
      setBackgroundPosition(prev => prev - gameSpeed);

      // Move gems and check collisions
      setGems(prevGems => {
        const updatedGems = prevGems.map(gem => ({
          ...gem,
          position: gem.position - gameSpeed
        })).filter(gem => gem.position > -100);

        // Check for collisions
        const playerGem = updatedGems.find(gem =>
          gem.lane === playerLane &&
          gem.position >= 200 &&
          gem.position <= 300
        );

        if (playerGem) {
          handleGemCollection(playerGem);
          return updatedGems.filter(g => g.id !== playerGem.id);
        }

        return updatedGems;
      });
    }, 50);

    return () => clearInterval(gameLoop);
  }, [gameStarted, gameOver, playerLane, gameSpeed, currentSentenceIndex, currentSegmentIndex]);

  const handleGemCollection = (gem: GemOption) => {
    const responseTime = segmentStartTime ? Date.now() - segmentStartTime.getTime() : 0;

    // Record the attempt
    const attempt: SegmentAttempt = {
      segmentId: gem.segmentId,
      selectedOptionId: gem.id,
      isCorrect: gem.isCorrect,
      responseTime,
      gemsEarned: gem.isCorrect ? 10 : 0
    };

    // Update session tracking
    if (gameSession) {
      setGameSession(prev => prev ? {
        ...prev,
        totalSegments: prev.totalSegments + 1,
        correctSegments: prev.correctSegments + (gem.isCorrect ? 1 : 0),
        incorrectSegments: prev.incorrectSegments + (gem.isCorrect ? 0 : 1),
        gemsCollected: prev.gemsCollected + attempt.gemsEarned,
        segmentAttempts: [...prev.segmentAttempts, attempt]
      } : null);
    }

    if (gem.isCorrect) {
      // Add to built sentence
      const currentSentence = sentences[currentSentenceIndex];
      const currentSegment = currentSentence?.segments[currentSegmentIndex];

      if (currentSegment) {
        setBuiltSentence(prev => [...prev, currentSegment.targetSegment]);
        setScore(prev => prev + 100 + attempt.gemsEarned);
        setFeedback({
          type: 'correct',
          text: `Correct! "${currentSegment.targetSegment}" +${100 + attempt.gemsEarned} points`
        });

        // Move to next segment or sentence
        setTimeout(() => {
          moveToNextSegment();
          setFeedback({ type: null, text: '' });
        }, 1500);
      }
    } else {
      setLives(prev => {
        const newLives = prev - 1;
        if (newLives <= 0) {
          setGameOver(true);
        }
        return newLives;
      });
      setFeedback({
        type: 'wrong',
        text: gem.explanation || 'Wrong! Try again'
      });
      setTimeout(() => setFeedback({ type: null, text: '' }), 2000);
    }
  };

  const moveToNextSegment = () => {
    const currentSentence = sentences[currentSentenceIndex];
    if (!currentSentence) return;

    if (currentSegmentIndex < currentSentence.segments.length - 1) {
      // Move to next segment in current sentence
      setCurrentSegmentIndex(prev => prev + 1);
      setSegmentStartTime(new Date());
    } else {
      // Sentence completed, move to next sentence
      if (currentSentenceIndex < sentences.length - 1) {
        setCurrentSentenceIndex(prev => prev + 1);
        setCurrentSegmentIndex(0);
        setBuiltSentence([]);
        setGameSpeed(prev => prev + 0.1); // Gradually increase difficulty
        setSegmentStartTime(new Date());
      } else {
        // All sentences completed
        setGameOver(true);
      }
    }
  };

  // Generate gems for current segment
  useEffect(() => {
    if (!gameStarted || gameOver || sentences.length === 0) return;

    const generateGems = () => {
      const currentSentence = sentences[currentSentenceIndex];
      const currentSegment = currentSentence?.segments[currentSegmentIndex];

      if (!currentSegment || currentSegment.options.length === 0) return;

      // Shuffle options and take up to 3
      const shuffledOptions = [...currentSegment.options].sort(() => Math.random() - 0.5);
      const selectedOptions = shuffledOptions.slice(0, 3);

      // Ensure we have exactly one correct option
      const hasCorrect = selectedOptions.some(opt => opt.isCorrect);
      if (!hasCorrect) {
        const correctOption = currentSegment.options.find(opt => opt.isCorrect);
        if (correctOption) {
          selectedOptions[Math.floor(Math.random() * selectedOptions.length)] = correctOption;
        }
      }

      const newGems: GemOption[] = selectedOptions.map((option, index) => ({
        id: option.id,
        text: option.optionText,
        isCorrect: option.isCorrect,
        lane: index,
        position: 800 + (index * 200),
        segmentId: currentSegment.id,
        explanation: option.explanation
      }));

      setGems(prev => [...prev, ...newGems]);
    };

    // Generate initial gems
    generateGems();

    // Generate new gems periodically
    const gemGenerator = setInterval(generateGems, 4000);

    return () => clearInterval(gemGenerator);
  }, [currentSentenceIndex, currentSegmentIndex, gameStarted, gameOver, sentences]);

  // Initialize game session when starting
  const initializeGameSession = () => {
    const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const session: GameSession = {
      sessionId,
      startTime: new Date(),
      totalSegments: 0,
      correctSegments: 0,
      incorrectSegments: 0,
      gemsCollected: 0,
      speedBoostsUsed: 0,
      segmentAttempts: []
    };

    setGameSession(session);
    setSegmentStartTime(new Date());
  };

  const startGame = (customSettings?: GameSettings) => {
    if (gameMode.type === 'free_play' && !customSettings) {
      setShowSettings(true);
      return;
    }

    if (customSettings) {
      setGameMode(prev => ({
        ...prev,
        language: customSettings.language,
        difficulty: customSettings.difficulty,
        theme: customSettings.theme,
        topic: customSettings.topic
      }));
      setLives(customSettings.livesCount);
    }

    if (sentences.length === 0) {
      alert('No sentences available. Please try again later.');
      return;
    }

    setGameStarted(true);
    setGameOver(false);
    setScore(0);
    setCurrentSentenceIndex(0);
    setCurrentSegmentIndex(0);
    setPlayerLane(1);
    setGems([]);
    setGameSpeed(2);
    setSpeedBoostActive(false);
    setBuiltSentence([]);
    setFeedback({ type: null, text: '' });
    initializeGameSession();
  };

  const resetGame = () => {
    setGameStarted(false);
    setGameOver(false);
    setScore(0);
    setLives(3);
    setCurrentSentenceIndex(0);
    setCurrentSegmentIndex(0);
    setPlayerLane(1);
    setGems([]);
    setGameSpeed(2);
    setSpeedBoostActive(false);
    setBuiltSentence([]);
    setFeedback({ type: null, text: '' });
    setGameSession(null);
    setSegmentStartTime(null);
  };

  // Save game session when game ends
  const saveGameSession = async () => {
    if (!gameSession || !user) return;

    try {
      const response = await fetch('/api/games/gem-collector/sessions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: gameSession.sessionId,
          assignmentId: gameMode.assignmentId,
          sessionType: gameMode.type,
          languagePair: `english_${gameMode.language}`,
          difficultyLevel: gameMode.difficulty,
          totalSentences: sentences.length,
          completedSentences: currentSentenceIndex + (currentSegmentIndex > 0 ? 1 : 0),
          totalSegments: gameSession.totalSegments,
          correctSegments: gameSession.correctSegments,
          incorrectSegments: gameSession.incorrectSegments,
          finalScore: score,
          gemsCollected: gameSession.gemsCollected,
          speedBoostsUsed: gameSession.speedBoostsUsed,
          segmentAttempts: gameSession.segmentAttempts,
          endedAt: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        console.error('Failed to save game session');
      }
    } catch (error) {
      console.error('Error saving game session:', error);
    }
  };

  // Save session when game ends
  useEffect(() => {
    if (gameOver && gameSession) {
      saveGameSession();
    }
  }, [gameOver, gameSession]);

  if (!gameStarted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center">
        <div className="bg-white rounded-3xl p-8 max-w-lg w-full mx-4 text-center shadow-2xl">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl">💎</span>
          </div>

          <h1 className="text-3xl font-bold text-slate-800 mb-4">
            Gem Collector
            {gameMode.type === 'assignment' && (
              <span className="block text-lg text-blue-600 font-normal mt-1">Assignment Mode</span>
            )}
          </h1>

          <p className="text-slate-600 mb-6 leading-relaxed">
            Build complete sentence translations word-by-word! Collect the correct translation gems
            in order to build each sentence. Use speed boost for extra challenge!
          </p>

          <div className="bg-slate-50 rounded-xl p-4 mb-6">
            <h3 className="font-semibold text-slate-800 mb-3">How to Play:</h3>
            <ul className="text-sm text-slate-600 space-y-2 text-left">
              <li>• Use ↑ and ↓ arrow keys to move between lanes</li>
              <li>• Collect gems with correct translation segments in order</li>
              <li>• Press → (right arrow) to activate speed boost</li>
              <li>• Build complete sentences to earn bonus points</li>
              <li>• You have 3 lives - avoid wrong gems!</li>
            </ul>
          </div>

          {sentences.length > 0 && (
            <div className="bg-blue-50 rounded-xl p-4 mb-6">
              <h3 className="font-semibold text-blue-800 mb-2">Ready to Start:</h3>
              <div className="text-sm text-blue-600">
                <p>📚 {sentences.length} sentences loaded</p>
                <p>🌍 Language: {gameMode.language.charAt(0).toUpperCase() + gameMode.language.slice(1)}</p>
                <p>📊 Difficulty: {gameMode.difficulty.charAt(0).toUpperCase() + gameMode.difficulty.slice(1)}</p>
                {gameMode.theme && <p>🎯 Theme: {gameMode.theme}</p>}
                {gameMode.topic && <p>📖 Topic: {gameMode.topic}</p>}
              </div>
            </div>
          )}

          <button
            onClick={() => startGame()}
            disabled={sentences.length === 0}
            className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl px-8 py-4 text-lg shadow-lg hover:shadow-xl transform transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            {sentences.length === 0 ? 'Loading Sentences...' :
             gameMode.type === 'assignment' ? 'Start Assignment! 💎' : 'Configure & Start! ⚙️💎'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 overflow-hidden relative">
      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-20 bg-black/20 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-3 flex items-center justify-between">
          <Link href="/games" className="text-white hover:text-blue-300 transition-colors">
            <ArrowLeft className="w-6 h-6" />
          </Link>

          <div className="flex items-center gap-4 text-white text-sm">
            <div className="text-center">
              <div className="text-xs opacity-80">Score</div>
              <div className="text-lg font-bold">{score}</div>
            </div>
            <div className="text-center">
              <div className="text-xs opacity-80">Lives</div>
              <div className="text-lg font-bold flex items-center gap-1">
                {Array.from({ length: lives }).map((_, i) => (
                  <span key={i} className="text-red-400">❤️</span>
                ))}
              </div>
            </div>
            <div className="text-center">
              <div className="text-xs opacity-80">Progress</div>
              <div className="text-lg font-bold">{currentSentenceIndex + 1}/{sentences.length}</div>
            </div>
            {speedBoostActive && (
              <div className="text-center">
                <div className="text-xs opacity-80">Speed Boost</div>
                <div className="text-lg font-bold flex items-center gap-1">
                  <Zap className="w-4 h-4 text-yellow-400" />
                  <span className="text-yellow-400">ACTIVE</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="absolute top-16 left-0 right-0 z-20">
        <div className="container mx-auto px-4">
          <div className="bg-white/20 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-blue-400 to-indigo-500 h-2 rounded-full transition-all duration-500"
              style={{ width: `${Math.min(100, progressPercentage)}%` }}
            />
          </div>
        </div>
      </div>

      {/* Current Sentence Building */}
      <div className="absolute top-24 left-0 right-0 z-20 text-center">
        <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-4 mx-4 shadow-lg">
          {currentSentence && (
            <>
              <div className="text-sm text-blue-600 font-semibold mb-2">
                Translate to {currentSentence.targetLanguage.charAt(0).toUpperCase() + currentSentence.targetLanguage.slice(1)}
              </div>
              <div className="text-lg font-bold text-slate-800 mb-3">
                {currentSentence.englishSentence}
              </div>

              {/* Built sentence so far */}
              <div className="bg-slate-100 rounded-xl p-3 mb-3">
                <div className="text-sm text-slate-600 mb-1">Building:</div>
                <div className="text-base font-semibold text-slate-800 min-h-[24px]">
                  {builtSentence.length > 0 ? builtSentence.join(' ') : '...'}
                  {currentSegment && (
                    <span className="text-blue-600 ml-2">
                      + "{currentSegment.englishSegment}"
                    </span>
                  )}
                </div>
              </div>

              {/* Current segment hint */}
              {currentSegment && (
                <div className="text-sm text-slate-600">
                  <span className="font-semibold">Next segment:</span> "{currentSegment.englishSegment}"
                  {currentSegment.grammarNote && (
                    <div className="text-xs text-blue-600 mt-1">
                      💡 {currentSegment.grammarNote}
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Game Area */}
      <div className="absolute inset-0 pt-48">
        {/* Background Pattern */}
        <div 
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: 'repeating-linear-gradient(90deg, transparent, transparent 50px, rgba(255,255,255,0.1) 50px, rgba(255,255,255,0.1) 52px)',
            transform: `translateX(${backgroundPosition % 100}px)`
          }}
        />

        {/* Lane Dividers */}
        <div className="absolute left-0 right-0 top-1/2 transform -translate-y-1/2 h-96">
          <div className="h-full relative">
            {/* Top Lane */}
            <div className="absolute top-0 left-0 right-0 h-1/3 border-b-2 border-white/20" />
            {/* Middle Lane */}
            <div className="absolute top-1/3 left-0 right-0 h-1/3 border-b-2 border-white/20" />
            {/* Bottom Lane */}
            <div className="absolute top-2/3 left-0 right-0 h-1/3" />
          </div>
        </div>

        {/* Player Character */}
        <div 
          className="absolute left-12 transition-all duration-200 z-10"
          style={{
            top: `calc(50% - 160px + ${playerLane * 128}px)`,
          }}
        >
          <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg transform hover:scale-110 transition-transform">
            <span className="text-2xl">🏃</span>
          </div>
        </div>

        {/* Gems */}
        {gems.map((gem) => (
          <div
            key={gem.id}
            className="absolute transition-all duration-100 z-10"
            style={{
              left: `${gem.position}px`,
              top: `calc(50% - 160px + ${gem.lane * 128}px)`,
            }}
          >
            <div className={`
              w-20 h-16 rounded-xl flex items-center justify-center text-white font-bold text-sm shadow-lg transform hover:scale-110 transition-all
              ${gem.isCorrect 
                ? 'bg-gradient-to-br from-emerald-400 to-green-500 shadow-green-500/30' 
                : 'bg-gradient-to-br from-red-400 to-pink-500 shadow-red-500/30'
              }
            `}>
              <div className="text-center">
                <div className="text-xl mb-1">
                  {gem.isCorrect ? '💎' : '💣'}
                </div>
                <div className="text-xs leading-tight">
                  {gem.text}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Enhanced Feedback with Animation */}
      {feedback.type && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30 animate-bounce">
          <div className={`
            px-8 py-4 rounded-2xl font-bold text-2xl shadow-2xl border-4 transform transition-all
            ${feedback.type === 'correct'
              ? 'bg-gradient-to-r from-green-500 to-emerald-600 border-green-300 text-white'
              : 'bg-gradient-to-r from-red-500 to-rose-600 border-red-300 text-white'
            }
          `}>
            <div className="flex items-center justify-center">
              <span className="mr-2 text-3xl">
                {feedback.type === 'correct' ? '✨' : '❌'}
              </span>
              {feedback.text}
            </div>
          </div>
        </div>
      )}

      {/* Combo/Streak Indicator */}
      {gameSession && gameSession.correctSegments > 2 && (
        <div className="absolute top-32 right-8 z-20">
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-4 py-2 rounded-full shadow-lg animate-pulse">
            <div className="text-sm font-bold">
              🔥 {gameSession.correctSegments} Streak!
            </div>
          </div>
        </div>
      )}

      {/* Game Over */}
      {gameOver && (
        <div className="absolute inset-0 bg-black/70 flex items-center justify-center z-40">
          <div className="bg-white rounded-3xl p-8 max-w-lg w-full mx-4 text-center shadow-2xl">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mx-auto mb-6">
              <span className="text-2xl">🏆</span>
            </div>

            <h2 className="text-3xl font-bold text-slate-800 mb-4">
              {currentSentenceIndex >= sentences.length - 1 ? 'Congratulations!' : 'Game Over!'}
            </h2>

            <div className="bg-slate-50 rounded-xl p-4 mb-6">
              <div className="text-2xl font-bold text-blue-600 mb-2">Final Score</div>
              <div className="text-4xl font-bold text-slate-800">{score}</div>

              <div className="grid grid-cols-2 gap-4 mt-4 text-sm">
                <div className="text-center">
                  <div className="text-slate-600">Sentences</div>
                  <div className="font-bold text-slate-800">
                    {currentSentenceIndex + (currentSegmentIndex > 0 ? 1 : 0)} / {sentences.length}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-slate-600">Accuracy</div>
                  <div className="font-bold text-slate-800">
                    {gameSession ? Math.round((gameSession.correctSegments / Math.max(1, gameSession.totalSegments)) * 100) : 0}%
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-slate-600">Gems Collected</div>
                  <div className="font-bold text-slate-800">{gameSession?.gemsCollected || 0}</div>
                </div>
                <div className="text-center">
                  <div className="text-slate-600">Speed Boosts</div>
                  <div className="font-bold text-slate-800">{gameSession?.speedBoostsUsed || 0}</div>
                </div>
              </div>
            </div>

            {gameMode.type === 'assignment' && (
              <div className="bg-blue-50 rounded-xl p-4 mb-6">
                <div className="text-sm text-blue-800">
                  📚 Assignment completed! Your progress has been saved.
                </div>
              </div>
            )}

            <div className="flex gap-4">
              <button
                onClick={resetGame}
                className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl px-6 py-3 shadow-lg hover:shadow-xl transform transition-all hover:scale-105"
              >
                Play Again
              </button>
              <Link
                href="/games"
                className="flex-1 bg-slate-200 text-slate-700 font-semibold rounded-xl px-6 py-3 text-center hover:bg-slate-300 transition-colors"
              >
                Back to Games
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-20">
        <div className="bg-black/50 backdrop-blur-sm rounded-xl px-4 py-2 text-white text-sm text-center">
          <div>↑ ↓ Move • → Speed Boost</div>
          {speedBoostActive && (
            <div className="text-yellow-400 text-xs mt-1">⚡ Speed Boost Active!</div>
          )}
        </div>
      </div>

      {/* Settings Modal */}
      <GemCollectorSettings
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        onStartGame={(settings) => {
          // Update game mode with new settings and fetch sentences
          const newGameMode = {
            ...gameMode,
            language: settings.language,
            difficulty: settings.difficulty,
            theme: settings.theme,
            topic: settings.topic
          };
          setGameMode(newGameMode);

          // Fetch new sentences with updated settings
          fetchSentences().then(() => {
            startGame(settings);
          });
        }}
      />
    </div>
  );
}