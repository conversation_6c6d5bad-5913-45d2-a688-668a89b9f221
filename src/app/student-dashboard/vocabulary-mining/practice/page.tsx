'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../../components/auth/AuthProvider';
import { useSupabase } from '../../../../components/supabase/SupabaseProvider';
import { VocabularyMiningService } from '../../../../services/vocabulary-mining';
import { 
  VocabularyGem, 
  GemCollection, 
  SessionType 
} from '../../../../types/vocabulary-mining';
import { 
  calculatePointsEarned,
  getGemInfo,
  determinePerformanceQuality 
} from '../../../../utils/vocabulary-mining';
import { 
  Pickaxe, 
  Star, 
  Clock, 
  Target, 
  Zap,
  CheckCircle,
  XCircle,
  ArrowRight,
  Home,
  RotateCcw
} from 'lucide-react';
import Link from 'next/link';

interface PracticeSession {
  sessionId: string;
  currentGem: VocabularyGem | null;
  currentIndex: number;
  totalGems: number;
  correctAnswers: number;
  startTime: Date;
  sessionGems: VocabularyGem[];
}

export default function VocabularyMiningPracticePage() {
  const { user } = useAuth();
  const { supabase } = useSupabase();
  const [miningService] = useState(() => new VocabularyMiningService(supabase));
  
  const [loading, setLoading] = useState(true);
  const [session, setSession] = useState<PracticeSession | null>(null);
  const [userAnswer, setUserAnswer] = useState('');
  const [showResult, setShowResult] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const [responseTime, setResponseTime] = useState(0);
  const [questionStartTime, setQuestionStartTime] = useState<Date | null>(null);
  const [sessionComplete, setSessionComplete] = useState(false);
  const [sessionStats, setSessionStats] = useState({
    totalTime: 0,
    accuracy: 0,
    gemsCollected: 0,
    pointsEarned: 0
  });

  useEffect(() => {
    if (user) {
      initializePracticeSession();
    }
  }, [user]);

  useEffect(() => {
    if (session?.currentGem && !showResult) {
      setQuestionStartTime(new Date());
    }
  }, [session?.currentGem, showResult]);

  const initializePracticeSession = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      
      // Get vocabulary gems for practice (mix of new and review items)
      const gems = await miningService.getVocabularyGems({ limit: 20 });
      
      if (gems.length === 0) {
        // No gems available
        setLoading(false);
        return;
      }
      
      // Start mining session
      const sessionId = await miningService.startMiningSession(
        user.id,
        'practice' as SessionType
      );
      
      // Initialize session
      const newSession: PracticeSession = {
        sessionId,
        currentGem: gems[0],
        currentIndex: 0,
        totalGems: gems.length,
        correctAnswers: 0,
        startTime: new Date(),
        sessionGems: gems
      };
      
      setSession(newSession);
      setLoading(false);
      
    } catch (error) {
      console.error('Error initializing practice session:', error);
      setLoading(false);
    }
  };

  const handleAnswerSubmit = async () => {
    if (!session || !session.currentGem || !questionStartTime) return;
    
    const currentTime = new Date();
    const timeTaken = currentTime.getTime() - questionStartTime.getTime();
    setResponseTime(timeTaken);
    
    // Check if answer is correct (case-insensitive)
    const correct = userAnswer.toLowerCase().trim() === session.currentGem.translation.toLowerCase().trim();
    setIsCorrect(correct);
    setShowResult(true);
    
    // Record the practice result
    try {
      await miningService.recordPracticeResult(
        session.sessionId,
        user!.id,
        session.currentGem.id,
        correct,
        timeTaken
      );
      
      if (correct) {
        setSession(prev => prev ? {
          ...prev,
          correctAnswers: prev.correctAnswers + 1
        } : null);
      }
    } catch (error) {
      console.error('Error recording practice result:', error);
    }
  };

  const handleNextQuestion = () => {
    if (!session) return;
    
    const nextIndex = session.currentIndex + 1;
    
    if (nextIndex >= session.totalGems) {
      // Session complete
      completeSession();
    } else {
      // Move to next question
      setSession({
        ...session,
        currentIndex: nextIndex,
        currentGem: session.sessionGems[nextIndex]
      });
      
      setUserAnswer('');
      setShowResult(false);
      setIsCorrect(false);
      setResponseTime(0);
    }
  };

  const completeSession = async () => {
    if (!session) return;
    
    try {
      // End the mining session
      const sessionResult = await miningService.endMiningSession(session.sessionId);
      
      const totalTime = Math.floor((new Date().getTime() - session.startTime.getTime()) / 1000);
      const accuracy = Math.round((session.correctAnswers / session.totalGems) * 100);
      
      setSessionStats({
        totalTime,
        accuracy,
        gemsCollected: session.correctAnswers,
        pointsEarned: sessionResult.sessionScore
      });
      
      setSessionComplete(true);
      
    } catch (error) {
      console.error('Error completing session:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !showResult && userAnswer.trim()) {
      handleAnswerSubmit();
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white">Preparing your mining session...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-xl p-8 max-w-md text-center">
          <Pickaxe className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">No Gems Available</h2>
          <p className="text-gray-600 mb-6">There are no vocabulary gems available for practice right now.</p>
          <Link
            href="/student-dashboard/vocabulary-mining"
            className="bg-indigo-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-indigo-700"
          >
            Back to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  if (sessionComplete) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-xl p-8 max-w-md text-center">
          <div className="text-6xl mb-4">🎉</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Mining Session Complete!</h2>
          
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-green-600">{sessionStats.gemsCollected}</div>
              <div className="text-sm text-gray-600">Gems Collected</div>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-blue-600">{sessionStats.accuracy}%</div>
              <div className="text-sm text-gray-600">Accuracy</div>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-purple-600">{sessionStats.pointsEarned}</div>
              <div className="text-sm text-gray-600">Points Earned</div>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-orange-600">{Math.floor(sessionStats.totalTime / 60)}m</div>
              <div className="text-sm text-gray-600">Time Spent</div>
            </div>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={initializePracticeSession}
              className="flex-1 bg-green-600 text-white px-4 py-3 rounded-lg font-medium hover:bg-green-700 flex items-center justify-center"
            >
              <RotateCcw className="w-5 h-5 mr-2" />
              Practice Again
            </button>
            
            <Link
              href="/student-dashboard/vocabulary-mining"
              className="flex-1 bg-gray-600 text-white px-4 py-3 rounded-lg font-medium hover:bg-gray-700 flex items-center justify-center"
            >
              <Home className="w-5 h-5 mr-2" />
              Dashboard
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const currentGem = session.currentGem;
  const gemInfo = currentGem ? getGemInfo(currentGem.gemType) : null;
  const progress = Math.round(((session.currentIndex + 1) / session.totalGems) * 100);

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
      {/* Header */}
      <div className="bg-white/10 backdrop-blur-sm border-b border-white/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Pickaxe className="w-6 h-6 text-yellow-400 mr-3" />
              <span className="text-white font-medium">Vocabulary Mining Practice</span>
            </div>
            
            <div className="flex items-center space-x-4 text-white">
              <div className="flex items-center">
                <Target className="w-4 h-4 mr-1" />
                <span className="text-sm">{session.currentIndex + 1}/{session.totalGems}</span>
              </div>
              <div className="flex items-center">
                <Star className="w-4 h-4 mr-1" />
                <span className="text-sm">{session.correctAnswers} correct</span>
              </div>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-4">
            <div className="w-full bg-white/20 rounded-full h-2">
              <div 
                className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {currentGem && (
          <div className="bg-white rounded-xl shadow-2xl p-8">
            {/* Gem Display */}
            <div className="text-center mb-8">
              <div 
                className="w-20 h-20 rounded-full mx-auto mb-4 flex items-center justify-center text-3xl border-4"
                style={{ 
                  backgroundColor: gemInfo?.color + '20',
                  borderColor: gemInfo?.color,
                  color: gemInfo?.color
                }}
              >
                {gemInfo?.icon}
              </div>
              <div className="text-sm font-medium text-gray-600 mb-2">
                {gemInfo?.name} Gem
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                {currentGem.term}
              </h2>
              {currentGem.exampleSentence && (
                <p className="text-gray-600 italic">
                  "{currentGem.exampleSentence}"
                </p>
              )}
            </div>

            {/* Answer Input */}
            {!showResult ? (
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    What does this word mean in English?
                  </label>
                  <input
                    type="text"
                    value={userAnswer}
                    onChange={(e) => setUserAnswer(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg text-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Type your answer..."
                    autoFocus
                  />
                </div>
                
                <button
                  onClick={handleAnswerSubmit}
                  disabled={!userAnswer.trim()}
                  className="w-full bg-indigo-600 text-white py-3 px-6 rounded-lg font-semibold text-lg hover:bg-indigo-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  Submit Answer
                </button>
              </div>
            ) : (
              /* Result Display */
              <div className="space-y-6">
                <div className={`text-center p-6 rounded-lg ${isCorrect ? 'bg-green-50' : 'bg-red-50'}`}>
                  <div className="flex items-center justify-center mb-4">
                    {isCorrect ? (
                      <CheckCircle className="w-12 h-12 text-green-500" />
                    ) : (
                      <XCircle className="w-12 h-12 text-red-500" />
                    )}
                  </div>
                  
                  <h3 className={`text-xl font-bold mb-2 ${isCorrect ? 'text-green-800' : 'text-red-800'}`}>
                    {isCorrect ? 'Correct!' : 'Incorrect'}
                  </h3>
                  
                  <p className="text-gray-700 mb-2">
                    <strong>Correct answer:</strong> {currentGem.translation}
                  </p>
                  
                  {!isCorrect && (
                    <p className="text-gray-600">
                      <strong>Your answer:</strong> {userAnswer}
                    </p>
                  )}
                  
                  <div className="flex items-center justify-center mt-4 text-sm text-gray-600">
                    <Clock className="w-4 h-4 mr-1" />
                    <span>{(responseTime / 1000).toFixed(1)}s</span>
                  </div>
                </div>
                
                <button
                  onClick={handleNextQuestion}
                  className="w-full bg-indigo-600 text-white py-3 px-6 rounded-lg font-semibold text-lg hover:bg-indigo-700 flex items-center justify-center transition-colors"
                >
                  {session.currentIndex + 1 >= session.totalGems ? 'Complete Session' : 'Next Gem'}
                  <ArrowRight className="w-5 h-5 ml-2" />
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
