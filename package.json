{"name": "language-gems", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "test:gem-collector": "vitest --run src/__tests__/**/*gem-collector*"}, "dependencies": {"@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@stripe/stripe-js": "^7.3.1", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.49.1", "@types/canvas-confetti": "^1.9.0", "autoprefixer": "^10.4.21", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.5.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.483.0", "next": "15.2.0", "postcss": "^8.5.5", "react": "^19.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "stripe": "^17.7.0", "tailwind-merge": "^3.0.2", "tailwindcss": "^3.4.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^15.0.0", "@testing-library/user-event": "^14.5.1", "@types/jspdf": "^1.3.3", "@types/node": "^20", "@types/react": "19.1.8", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.1.1", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "eslint": "^9", "eslint-config-next": "15.2.0", "jsdom": "^23.0.1", "node-mocks-http": "^1.13.0", "typescript": "5.8.3", "vitest": "^1.0.4"}}