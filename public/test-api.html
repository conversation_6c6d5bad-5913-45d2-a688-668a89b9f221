<!DOCTYPE html>
<html>
<head>
    <title>Test Assignment Progress API</title>
</head>
<body>
    <h1>Assignment Progress API Test</h1>
    <button onclick="testAPI()">Test API Endpoint</button>
    <div id="results"></div>

    <script>
        async function testAPI() {
            const testData = {
                status: 'completed',
                score: 85,
                accuracy: 85,
                timeSpent: 120,
                attempts: 10,
                gameSession: {
                    sessionData: {
                        matches: 8,
                        attempts: 10,
                        gameType: 'memory-game',
                        timeSpent: 120
                    },
                    vocabularyPracticed: [1, 2, 3, 4, 5],
                    wordsCorrect: 8,
                    wordsAttempted: 10
                },
                vocabularyProgress: [
                    {
                        vocabularyId: 1,
                        attempts: 2,
                        correctAttempts: 1,
                        responseTime: 3.5,
                        wasCorrect: true
                    },
                    {
                        vocabularyId: 2,
                        attempts: 1,
                        correctAttempts: 1,
                        responseTime: 2.1,
                        wasCorrect: true
                    }
                ]
            };

            try {
                const response = await fetch('/api/assignments/1/progress', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData),
                });

                const responseText = await response.text();
                document.getElementById('results').innerHTML = `
                    <h3>Response Status: ${response.status}</h3>
                    <pre>${responseText}</pre>
                `;
            } catch (error) {
                document.getElementById('results').innerHTML = `
                    <h3>Error:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }
    </script>
</body>
</html>
